""" Calculate bank deposits accruals """
# %%
import pandas as pd
from datetime import datetime, timedelta

# Get BNR file
file = 'Accruals_FX_template.xlsx'
# file = 'Extra_depozite.xlsx'

# FX mapping to day
bnr = pd.read_excel(file, sheet_name='BNR').sort_values(by='date')
bnr['RON'] = 1
# bnr['date'] = bnr['date'] .apply(lambda x: datetime.strptime(str(x)[:10], '%Y-%m-%d').date())
bnr['date'] = bnr['date'].dt.date
bnr_map_eur = bnr.set_index('date')['EUR'].to_dict()
bnr_map_usd = bnr.set_index('date')['USD'].to_dict()
bnr_map_ron = bnr.set_index('date')['RON'].to_dict()

# Input depozite
depozite = pd.read_excel(file, sheet_name='depozite').sort_values(by='constituire')
depozite[['constituire', 'scadenta']] = depozite[['constituire', 'scadenta']].apply(lambda x: x.dt.date)

print(depozite)

lista_depozite = []
for index, row in depozite.iterrows():
    lista_depozite += [
        {
            'transactionid': row['transaction_id'], 'valuta': row['valuta'], 'custodian': row['custodian'],
            'principal': row['principal'], 'dobanda': row['dobanda'], 'conventie': row['conventie'],
            'constituire': row['constituire'], 'scadenta': row['scadenta'],
            'incasat': row['dobanda incasata'],
            'prima_constituire': row['prima_constituire'],
            'ultima_lichidare': row['ultima_lichidare'],
        }
    ]

# Inputs
inputs = lista_depozite[21]
print(inputs)
print(bnr_map_eur)
display(bnr)

lista_depozite


# %%

# Function to replicate the logic from Excel to Python
def calculate_accruals_fx_with_formulas(inputs):
    # Extract input values

    principal = inputs['principal']
    interest_rate = inputs['dobanda'] 
    convention = inputs['conventie']
    start_date = inputs['constituire']
    end_date = inputs['scadenta']
    currency = inputs['valuta']
    id_depozit = inputs['transactionid']
    custodian = inputs['custodian']
    incasat = inputs['incasat']
    prima_constituire = inputs['prima_constituire']
    ultima_lichidare = inputs['ultima_lichidare']

    if currency == 'EUR':
        bnr_map = bnr_map_eur
    elif currency == 'USD':
        bnr_map = bnr_map_usd
    elif currency == 'RON':
        bnr_map = bnr_map_ron
    else:
        raise ValueError('Invalid currency')

    # Calculate end of months between start_date and minimim of today and end_date
    max_date = datetime.today().replace(day=1).date()
    end_of_months = pd.date_range(start=start_date, end=min(max_date, end_date), freq='ME') 
    end_of_months = [x.date() for x in end_of_months]
    # print(' '.join([str(x) for x in end_of_months]))
    
    # Initialize result list for rows
    results = []
    
    # Step 1: CONSTITUIRE_DEP_VALUTA (Initial deposit setup, principal converted to local currency with the rate valid for the previous day)
    bnr_date = max(bnr[bnr['date']<start_date]['date'])
    initial_bnr_rate = bnr_map[bnr_date]
    initial_value_ron = principal * initial_bnr_rate
    # print(start_date, initial_bnr_rate, initial_value_ron)
    
    # Add row for CONSTITUIRE_DEP_VALUTA
    new_row = {
        'date': start_date,
        'operation': 'CONSTITUIRE_DEP_VALUTA' if currency != 'RON' else 'CONSTITUIRE_DEP_LEI',
        'value': -principal,
        'currency': currency,
        'bnr': initial_bnr_rate,
        'value_ron': initial_value_ron,
        'quantity': principal,
        'transactionid': str(id_depozit) + ' CONSTITUIRE ' + str(start_date),
    }
    if prima_constituire:
        new_row['idx'] = 'ADAUGA'
    results.append(new_row)
    
    # Step 2: Calculate interest accrual for each month until the end date
    last_accrual_date = start_date
    last_bnr_rate = initial_bnr_rate
    last_bnr_rate_end = initial_bnr_rate
    accrued_interest_cumulative = 0
    accrued_interest_cumulative_ron = 0
    accrued_fx_cumulative_ron = 0

    # Iterate over end of months
    for date in end_of_months:
        # print(date)
        accrual_days = (date - last_accrual_date).days
        last_accrual_date = date

        # Get BNR rate for the current date and rate for the end of month
        bnr_date = max(bnr[bnr['date']<date]['date'])
        bnr_date_end = max(bnr[bnr['date']<=date]['date'])
        bnr_rate = bnr_map[bnr_date]
        bnr_rate_end = bnr_map[bnr_date_end]

        # Accrued interest
        accrued_interest = (principal * interest_rate * accrual_days) / convention
        accrued_interest_ron = accrued_interest * bnr_rate
        accrued_interest_cumulative += accrued_interest
        accrued_interest_cumulative_ron += accrued_interest_ron
        results.append({
            'date': date,
            'operation': 'ACCRUAL_INTEREST_VALUTA' if currency != 'RON' else 'ACCRUAL_INTEREST_LEI',
            'value': accrued_interest,
            'cumulative_interest': accrued_interest_cumulative,
            'currency': currency,
            'bnr': bnr_rate,
            'value_ron': accrued_interest_ron,
            'transactionid': str(id_depozit) + ' ACC ' + str(date),
        })

        # Diferente de curs valutar la depozit
        if currency != 'RON':
            print(bnr_rate_end, last_bnr_rate_end)
            
            accrued_fx_depozit_ron = principal * (bnr_rate_end - last_bnr_rate_end)
            results.append({
                'date': date,
                'operation': 'FX_DIFF_DEP_PLUS' if accrued_fx_depozit_ron>=0 else 'FX_DIFF_DEP_MINUS',
                'value': 0,
                'currency': currency,
                'bnr': bnr_rate_end,
                'value_ron': accrued_fx_depozit_ron,
                'transactionid': str(id_depozit) + ' FXDEP ' + str(date),
            })

        # Diferente de curs valutar la dobanzi
        if currency != 'RON':
            accrued_fx_interest_ron = accrued_interest_cumulative * bnr_rate_end - accrued_interest_cumulative_ron - accrued_fx_cumulative_ron
            accrued_fx_cumulative_ron += accrued_fx_interest_ron
            results.append({
                'date': date,
                'operation': 'FX_DIFF_ACCRUAL_PLUS' if accrued_fx_interest_ron>=0 else 'FX_DIFF_ACCRUAL_MINUS',
                'value': 0,
                'currency': currency,
                'bnr': bnr_rate_end,
                'value_ron': accrued_fx_interest_ron,
                'transactionid': str(id_depozit) + ' FXINT ' + str(date),
            })  

        # Reset reference exchange rates
        last_bnr_rate_end = bnr_rate_end
        last_bnr_rate = bnr_rate
         

    # Step 3: Compute and store final values
    if end_date <= max_date:
        
        # Get BNR rate for the maturity date 
        bnr_date = max(bnr[bnr['date']<end_date]['date'])
        bnr_rate = bnr_map[bnr_date]
        accrual_days = (end_date - last_accrual_date).days

        # if len(end_of_months) > 0:
        #     # Capitalizare acruals
        #     results.append({
        #         'date': end_date,
        #         'operation': 'CAPITALIZARE_ACCRUALS',
        #         'value': accrued_interest_cumulative,
        #         'currency': currency,
        #         'bnr': bnr_rate,
        #         'value_ron': accrued_interest_cumulative * bnr_rate,
        #         'transactionid': str(id_depozit) + ' CAPAC ' + str(end_date),
        #     })  

        # Rest dobanzi
        
        # accrued_interest = (principal * interest_rate * accrual_days) / convention
        # accrued_interest_cumulative += accrued_interest
        # accrued_interest_cumulative_ron += accrued_interest * bnr_rate
        # new_row = {
        #     'date': end_date,
        #     'operation': 'INC_DOBANDA_LEI' if currency=='RON' else 'INC_DOBANDA_VALUTA',
        #     'value': accrued_interest,
        #     'cumulative_interest': accrued_interest_cumulative,
        #     'currency': currency,
        #     'bnr': bnr_rate,
        #     'value_ron': accrued_interest * bnr_rate,
        #     'incasat': incasat,
        #     'calculat_vs_incasat': round(accrued_interest_cumulative - incasat, 2),
        #     'transactionid': str(id_depozit) + ' REST ' + str(end_date),
        # }
        # if ultima_lichidare:
        #     new_row['idx'] = 'ADAUGA'
        # results.append(new_row)

        if abs(accrued_interest_cumulative - incasat) > 0.01:
            print('Diferente dobanzi: accrued_interest_cumulative', accrued_interest_cumulative, 'incasat', incasat, id_depozit)

        # Adaugare dobanda incasata si storno accrued_interest_cumulative
        if incasat>0:
            new_row = {
                'date': end_date,
                'operation': 'INC_DOBANDA_LEI' if currency=='RON' else 'INC_DOBANDA_VALUTA',
                'value': incasat,
                'cumulative_interest': accrued_interest_cumulative,
                'currency': currency,
                'bnr': bnr_rate,
                'value_ron': incasat * bnr_rate,
                'incasat': incasat,
                'calculat_vs_incasat': round(accrued_interest_cumulative - incasat, 2),
                'transactionid': str(id_depozit) + ' DOBANDA ' + str(end_date),
            }
            if ultima_lichidare:
                new_row['idx'] = 'ADAUGA'
            results.append(new_row)
        if accrued_interest_cumulative>0:
            new_row = {
                    'date': end_date,
                    'operation': 'ACCRUAL_INTEREST_LEI' if currency=='RON' else 'ACCRUAL_INTEREST_LEI_VALUTA',
                    'value': -accrued_interest_cumulative,
                    'currency': currency,
                    'bnr': bnr_rate,
                    'storno': True,
                    'value_ron': -accrued_interest_cumulative * bnr_rate,
                    'transactionid': str(id_depozit) + ' REVERSAL ' + str(end_date),
                }
            if ultima_lichidare:
                new_row['idx'] = 'ADAUGA'
            results.append(new_row)


        # Diferente de curs valutar la depozit
        if currency != 'RON':
            accrued_fx_depozit_ron = principal * (bnr_rate - last_bnr_rate_end)
            results.append({
                'date': end_date,
                'operation': 'FX_DIFF_DEP_PLUS' if accrued_fx_depozit_ron>=0 else 'FX_DIFF_DEP_MINUS',
                'value': 0,
                'currency': currency,
                'bnr': bnr_rate,
                'value_ron': accrued_fx_depozit_ron,
                'transactionid': str(id_depozit) + ' FXDEP ' + str(end_date),
            })

        if len(end_of_months) > 0:
            # Diferente de curs valutar la dobanzi
            if currency != 'RON':
                accrued_fx_interest_ron = accrued_interest_cumulative * bnr_rate - accrued_interest_cumulative_ron - accrued_fx_cumulative_ron
                # print('accrued_interest_cumulative', accrued_interest_cumulative)
                # print('bnr_rate', bnr_rate) 
                # print('accrued_interest_cumulative_ron', accrued_interest_cumulative_ron)
                # print('accrued_fx_cumulative_ron', accrued_fx_cumulative_ron)
                results.append({
                    'date': end_date,
                    'operation': 'FX_DIFF_ACCRUAL_PLUS' if accrued_fx_interest_ron>=0 else 'FX_DIFF_ACCRUAL_MINUS',
                    'value': 0,
                    'currency': currency,
                    'bnr': bnr_rate,
                    'value_ron': accrued_fx_interest_ron,
                    'transactionid': str(id_depozit) + ' FXINT ' + str(end_date),
                })  

        # Maturitate depozit
        new_row = {
            'date': end_date,
            'operation': 'MATURITATE_DEP_VALUTA' if currency != 'RON' else 'MATURITATE_DEP_LEI',
            'value': principal,
            'currency': currency,
            'bnr': bnr_rate,
            'value_ron': principal * bnr_rate,
            'quantity': -principal,
            'transactionid': str(id_depozit) + ' MAT ' + str(end_date),
        }
        if ultima_lichidare:
            new_row['idx'] = 'ADAUGA'
        results.append(new_row)


    # Convert results into a DataFrame
    output_df = pd.DataFrame(results)
    roundx = 3
    output_df['value'] = output_df['value'].round(roundx)
    output_df['value_ron'] = output_df['value_ron'].round(roundx)
    # output_df['cumulative_interest'] = output_df['cumulative_interest'].round(roundx)
    
    output_df['custodian'] = custodian
    
    output_df['ubo'] = "DD"
    output_df['partner'] = output_df['custodian']
    output_df['account'] = output_df['custodian'] + '_' + output_df['currency']
    output_df['instrument'] = str(id_depozit) 
    # output_df['instrument__type'] = 'DEPOZIT'
    output_df['details'] = str(id_depozit) + ' ' + output_df['operation']
    output_df['quantity'] = output_df['quantity'].fillna(0)

    if 'storno' not in output_df.columns:
        output_df['storno'] = False
    else:
        output_df['storno'] = output_df['storno'].astype(bool).fillna(False)

    # # Adaugare coloane care ar putea lipsi
    # for col in ['incasat', 'calculat_vs_incasat', ]:
    #     if col not in output_df.columns:
    #         output_df[col] = None

    output_df['partner'] = output_df['custodian']
    cols = [
        # 'id', 'storno',
        'ubo', 'custodian', 'partner', 'account', 'operation', 'instrument', 
        # 'instrument__type',
        'date', 
        'transactionid', 'value', 'bnr', 'value_ron',
        'quantity', 'details', 'storno',
        'currency', 
        # 'incasat', 'calculat_vs_incasat', 
        ]

    if 'idx' in output_df.columns:
        cols.append('idx')

    return output_df[cols]


# Generate the output DataFrame
inputs = lista_depozite[55]
output_df = calculate_accruals_fx_with_formulas(inputs)

# Display the output
display(output_df)


# %%

df = pd.DataFrame()

for inputs in lista_depozite:
    # print(inputs)
    dx = calculate_accruals_fx_with_formulas(inputs)
    df = pd.concat([df, dx], ignore_index=True)

timestamp = datetime.now().strftime("%Y-%m-%d %H-%M")
df['id'] = None
df['storno'] = False

df.to_excel(f'./save/accruals {timestamp}.xlsx', index=False)

display(df)

print('DONE')
# %%
