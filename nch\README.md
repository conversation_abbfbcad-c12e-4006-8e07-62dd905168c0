# NCH Investment Management System

A comprehensive Django-based portfolio and accounting management system for investment operations, featuring automated broker integrations, financial calculations, and regulatory reporting capabilities.

## Github Repo: https://github.com/nchinvest/nch


## Table of Contents

- [Overview](#overview)
- [Technical Stack](#technical-stack)
- [Installation](#installation)
- [Core Features](#core-features)
- [Data Models](#data-models)
- [Broker Integrations](#broker-integrations)
- [Financial Calculations](#financial-calculations)
- [Management Commands](#management-commands)
- [Admin Interface](#admin-interface)
- [Web Interface](#web-interface)
- [Automated Workflows](#automated-workflows)
- [Export Capabilities](#export-capabilities)
- [Security Features](#security-features)
- [API Documentation](#api-documentation)
- [Troubleshooting](#troubleshooting)

## Overview

The NCH Investment Management System is designed to handle complex multi-broker, multi-currency investment portfolios with automated data import, sophisticated financial calculations, and comprehensive reporting capabilities. The system integrates with major brokers and provides real-time portfolio valuation, accrual calculations, and regulatory compliance reporting.

## Technical Stack

### Infrastructure
- **OS**: Debian Linux
- **CPU**: 2 virtual cores
- **Memory**: 8GB RAM
- **Database**: PostgreSQL 16.4

### Core Technologies
- **Python**: 3.12
- **Django**: 5.1.2
- **Database ORM**: Django ORM with PostgreSQL backend
- **Task Scheduling**: Cron jobs for automated workflows
- **Financial Libraries**: QuantLib for bond calculations
- **Data Processing**: Pandas, NumPy
- **Web Framework**: Django with Bootstrap 5

### Dependencies
```python
# Key packages
django==5.1.2
pandas
numpy
quantlib-python
openpyxl
xmltodict
requests
websocket-client
dbf
simple-history
django-tables2
django-import-export
```

## Installation

### Prerequisites
```bash
# Install system dependencies
sudo apt update
sudo apt install python3.12 python3.12-venv postgresql-16 postgresql-contrib
```

### Setup
```bash
# Clone repository
git clone https://github.com/nchinvest/nch
cd nch

# Create virtual environment
python3.12 -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Database setup
createdb nch_db
python manage.py migrate

# Create superuser
python manage.py createsuperuser

# Collect static files
python manage.py collectstatic
```

### Environment Configuration
Create `.env` file:
```env
SECRET_KEY=your_secret_key
DB_PASS=your_db_password
DB_HOST=localhost
IBKR_TOKEN=your_ibkr_token
IBKR_QUERY=your_ibkr_query_id
USER_TDV=your_tradeville_username
PASS_TDV=your_tradeville_password
TDV_DD=tradeville_person_1
TDV_AS=tradeville_person_2
TDV_CP=tradeville_person_3
```

## Core Features

### 1. Multi-Broker Portfolio Management
- **Supported Brokers**: Interactive Brokers (IBKR), Tradeville (TDV), Morgan Stanley (MST)
- **Real-time Data**: Automated daily import of positions, transactions, and market data
- **Multi-Currency Support**: EUR, USD, RON, GBP, CAD, SEK, MXN with automatic FX conversion
- **Position Tracking**: Real-time portfolio valuation with cost basis tracking

### 2. Automated Exchange Rate Management
- **BNR Integration**: Daily import of Romanian National Bank exchange rates
- **Historical Data**: Historical exchange rate database
- **FX Difference Calculations**: Automatic calculation of foreign exchange gains/losses
- **Rate Validation**: Built-in validation against broker-reported rates

### 3. Advanced Financial Calculations
- **Bond Accruals**: QuantLib-powered bond interest accrual calculations
- **Deposit Accruals**: Bank deposit interest calculations with multiple conventions
- **Cost Basis Tracking**: Weighted average cost method implementation

### 4. Regulatory Compliance
- **Journal Entries**: Accounting journal with double-entry bookkeeping
- **Audit Trail**: Transaction history with change tracking
- **Lock Mechanism**: Period-end locking to prevent unauthorized changes

## Data Models

### Core Entities

#### Currency
```python
# Supported currencies with BNR exchange rates
currency_code: CharField(3)  # EUR, USD, RON, etc.
currency_name: CharField(100)
```

#### UBO (Ultimate Beneficial Owner)
```python
ubo_code: CharField(100)     # Entity identifier
ubo_name: CharField(100)     # Entity name
ubo_details: CharField(100)  # Additional details
```

#### Custodian
```python
custodian_code: CharField(10)    # IBKR, TDV, MST, etc.
custodian_type: ForeignKey       # Partner type classification
custodian_name: CharField(100)   # Full custodian name
```

#### Instrument
```python
symbol: CharField(100)              # Instrument identifier
isin: CharField(12)                # International identifier
name: CharField(100)               # Full instrument name
type: CharField(100)               # BOND, STOCK, CASH, DEPOSIT
face_value: FloatField             # Nominal value for bonds
interest: FloatField               # Coupon rate for bonds
maturity: DateField                # Maturity date
convention: CharField              # Day count convention
calendar: CharField                # Holiday calendar
bond_coupon_count: IntegerField    # Annual coupon frequency
```

#### Journal
```python
# Double-entry accounting journal
ubo: ForeignKey(Ubo)
custodian: ForeignKey(Custodian)
account: ForeignKey(Account)
operation: ForeignKey(Operation)
partner: ForeignKey(Partner)
instrument: ForeignKey(Instrument)
date: DateField
transactionid: CharField(100)
value: FloatField                  # Transaction value in original currency
value_ron: FloatField             # Value in RON for reporting
bnr: FloatField                   # Exchange rate used
quantity: FloatField              # Quantity traded/held
details: CharField(250)           # Transaction description
storno: BooleanField              # Reversal flag
lock: BooleanField                # Period lock flag
```

### Financial Models

#### BNR (Exchange Rates)
```python
currency_code: ForeignKey(Currency)
date: DateField
value: FloatField           # Rounded rate for reporting
value_exact: FloatField     # Exact rate for calculations
```

#### Portfolio
```python
ubo: ForeignKey(Ubo)
instrument: ForeignKey(Instrument)
date: DateField
cost: FloatField           # Cost basis
value: FloatField          # Market value
quantity: FloatField       # Position size
accruedint: FloatField     # Accrued interest
```

#### Deposits
```python
deposit: ForeignKey(Instrument)
principal: DecimalField(15,2)
interest_rate: FloatField
start: DateField
maturity: DateField
convention: CharField      # 360, 365, ACT/ACT
interest_amount: DecimalField(15,2)    # Actual interest received
new_deposit: BooleanField             # First deposit flag
liquidated: BooleanField              # Final withdrawal flag
```

## Broker Integrations

### Interactive Brokers (IBKR)
**File**: `brokers/management/commands/lib/ibkr.py`

#### Features
- **Flex Query API**: Automated report generation and download
- **Report Types**: Positions, cash, transactions, corporate actions, taxes
- **Settlement Calculation**: T+1/T+2 settlement date calculation
- **Data Validation**: Automatic reconciliation of positions vs. transactions

#### Usage
```python
# Initialize IBKR API
ibkr = IbkrApi()

# Execute flex query
reports = ibkr.flex_query(query_id=settings.IBKR_QUERY)

# Convert to pandas DataFrames
tables = ibkr.xml_to_pandas()
```

#### Management Commands
```bash
# Download IBKR data
python manage.py api_ibkr

# Import portfolio positions
python manage.py upload_ibkr_port

# Import transactions
python manage.py uptrz_ibkr
```

### Tradeville (TDV)
**File**: `brokers/management/commands/lib/api_tdv.py`

#### Features
- **WebSocket API**: Real-time connection to Tradeville portal
- **Multi-Account Support**: Handle multiple sub-accounts per user
- **Position Data**: Portfolio holdings and valuations
- **Transaction History**: Trading activity

#### Key Methods
```python
# Initialize WebSocket connection
tdv = MyWebSocket()

# Get portfolio positions
portfolio = tdv.portof()

# Get transaction history
transactions = tdv.activitate()

# Get order book
level2_data = tdv.level2(symbol)

# Place order
result = tdv.ordin(symbol, price, quantity, side)
```

#### Management Commands
```bash
# Download TDV data
python manage.py api_tdv

# Import portfolio
python manage.py upload_tdv_port

# Import transactions
python manage.py uptrz_tdv
```

### Morgan Stanley (MST)
**File**: `brokers/management/commands/_upload_mst_port.py`

#### Features
- **Excel Import**: Process holdings and activity reports
- **Instrument Registration**: Automatic instrument creation
- **Portfolio Import**: Position and valuation data

## Financial Calculations

### Bond Accruals
**File**: `port/management/commands/bond_accruals_ql.py`

#### Features
- **QuantLib Integration**: Professional-grade bond mathematics
- **Multiple Conventions**: ACT/ACT, 30/360, ACT/360, ACT/365
- **Holiday Calendars**: US Government Bond, TARGET, NYSE calendars
- **Coupon Schedules**: Annual and semi-annual coupon bonds
- **Settlement Rules**: T+1/T+2 with proper business day adjustment

#### Calculation Process
1. **Schedule Generation**: Create coupon payment schedule
2. **Accrual Calculation**: Daily interest accrual using QuantLib
3. **FX Revaluation**: Month-end FX difference calculations
4. **Journal Generation**: Automatic accounting entries

#### Usage
```bash
# Calculate bond accruals
python manage.py bond_accruals_ql
```

### Deposit Accruals
**File**: `port/management/commands/deposit_accruals.py`

#### Features
- **Multiple Conventions**: 360/365 day count methods
- **FX Calculations**: Principal and interest revaluation
- **Month-end Processing**: Automatic accrual and revaluation entries
- **Maturity Handling**: Interest receipt and principal repayment

#### Calculation Logic
```python
# Monthly accrual calculation
accrued_interest = (principal * interest_rate * days) / convention

# FX difference on principal
fx_diff_principal = principal * (rate_end - rate_start)

# FX difference on accrued interest
fx_diff_interest = accrued_total * rate_end - accrued_ron_total
```

### Exchange Rate Management
**File**: `port/management/commands/curs_bnr.py`

#### Features
- **Daily BNR Import**: Automatic Romanian National Bank rate download
- **Historical Data**: Support for multi-year rate history
- **Business Day Logic**: Proper handling of weekends and holidays
- **Rate Validation**: Cross-checking against broker-reported rates

## Management Commands

### Data Import Commands

#### `curs_bnr`
- **Purpose**: Import Romanian National Bank exchange rates
- **Frequency**: Daily (automated via cron)
- **Data Source**: BNR XML API
- **Output**: Updates BNR table with latest rates

#### `api_ibkr`
- **Purpose**: Download IBKR Flex reports
- **Frequency**: Daily (automated via cron)
- **Authentication**: Uses IBKR token from settings
- **Output**: Raw XML files saved to file system

#### `api_tdv`
- **Purpose**: Download Tradeville data via WebSocket
- **Frequency**: Daily (automated via cron)
- **Authentication**: Username/password from settings
- **Output**: CSV files with positions and transactions

### Data Processing Commands

#### `upload_ibkr_port`
- **Purpose**: Import IBKR portfolio positions to database
- **Dependencies**: Requires api_ibkr to run first
- **Processing**: Instrument creation, position updates
- **Validation**: Automatic reconciliation checks

#### `upload_tdv_port`
- **Purpose**: Import TDV portfolio positions to database
- **Dependencies**: Requires api_tdv to run first
- **Processing**: Multi-account position aggregation
- **Currency Handling**: Automatic FX conversion

#### `uptrz_ibkr`
- **Purpose**: Generate accounting journal entries from IBKR data
- **Features**: 
  - Transaction classification
  - Cost basis calculation
  - FX transaction handling
  - Commission and tax processing
- **Output**: Excel file for manual review

#### `uptrz_tdv`
- **Purpose**: Generate accounting journal entries from TDV data
- **Features**:
  - Transaction parsing and classification
  - Interest accrual separation
  - Commission handling
  - Internal transfer recognition
- **Validation**: Position and cash reconciliation

#### `uptrz_manual`
- **Purpose**: Import manually prepared journal entries
- **Format**: Excel files with predefined structure
- **Features**: Cost basis calculation, profit calculation
- **Usage**: For bank statements and other manual data

### Financial Calculation Commands

#### `bond_accruals_ql`
- **Purpose**: Calculate bond interest accruals using QuantLib
- **Features**:
  - Multiple day count conventions
  - Holiday calendar support
  - Coupon schedule generation
  - FX revaluation calculations
- **Output**: Detailed Excel workbook with calculations

#### `deposit_accruals`
- **Purpose**: Calculate bank deposit interest accruals
- **Features**:
  - Multiple interest conventions
  - Monthly accrual calculations
  - FX difference calculations
  - Maturity processing
- **Output**: Excel file with journal entries

#### `dif_fx`
- **Purpose**: Calculate cumulative FX differences from FX transactions
- **Logic**: Daily aggregation of FX gains/losses
- **Output**: FX difference journal entries

#### `fix_bnr_rates`
- **Purpose**: Validate and correct BNR exchange rates in journal
- **Features**: 
  - Rate comparison against BNR database
  - Error identification and reporting
  - Correction suggestions
- **Output**: Excel file with rate errors

## Admin Interface

### Features

#### Custom Admin Views
- **Enhanced List Views**: Sortable, filterable tables
- **Bulk Operations**: Mass import/export capabilities
- **Custom Actions**: Specialized operations per model
- **Validation**: Built-in data validation and error checking

#### Import/Export Capabilities
- **Excel Support**: Native Excel import/export
- **Foreign Key Handling**: Intelligent FK resolution
- **Bulk Operations**: Efficient mass data operations
- **Error Handling**: Comprehensive error reporting

#### Journal Management
- **Export to Excel**: Formatted accounting journal export
- **DBF Export**: Legacy accounting system compatibility
- **Analytic Accounts**: Automatic analytic account generation
- **History Tracking**: Audit trail using django-simple-history

### Custom Admin Actions

#### BNR Admin
- **Update Rates Button**: Manual BNR rate refresh
- **Excel Export**: Formatted rate export

#### Custodian Admin
- **Export Buttons**: Direct access to broker data export
- **File Download**: Automatic file generation and download

#### Journal Admin
- **Lock Mechanism**: Period-end locking functionality
- **Rate Validation**: BNR rate error checking
- **Excel Export**: Professional accounting format
- **DBF Export**: Legacy system compatibility

#### Deposits Admin
- **Accrual Calculation**: Direct accrual calculation trigger
- **File Download**: Automatic result export

## Web Interface

### Portfolio Views

#### `/portfolio/`
- **Manual Formatting**: Custom HTML table layout
- **Latest Positions**: Most recent portfolio snapshot
- **Multi-Custodian**: Aggregated view across all brokers

#### `/port/`
- **Auto-formatted**: Django-tables2 with sorting
- **Interactive**: Sortable columns, pagination
- **Responsive**: Bootstrap-based responsive design

### Journal Views

#### `/jurnal/`
- **Journal**: All accounting entries
- **Sortable Table**: JavaScript-based sorting
- **SAF-T Format**: Romanian tax compliance format
- **Analytic Accounts**: Automatic analytic account display

#### `/map_operations/`
- **Operation Mapping**: Debit/credit account mapping
- **Chart of Accounts**: Account structure
- **Reference Table**: Operation code lookup

## Automated Workflows

### Cron Job Schedule
**File**: `crontab.txt`

#### Exchange Rates
```bash
# BNR rates - twice daily with retry
30 13 * * 1,2,3,4,5 python manage.py curs_bnr
30 15 * * 1,2,3,4,5 python manage.py curs_bnr
```

#### IBKR Data
```bash
# IBKR data - twice daily with processing
15 8 * * 1,2,3,4,5,6 python manage.py api_ibkr
45 8 * * 1,2,3,4,5,6 python manage.py api_ibkr
15 9 * * 1,2,3,4,5,6 python manage.py upload_ibkr_port
```

#### Tradeville Data
```bash
# TDV data - twice daily with processing
20 7 * * 1,2,3,4,5,6 python manage.py api_tdv
30 15 * * 1,2,3,4,5,6 python manage.py api_tdv
40 7 * * 1,2,3,4,5,6 python manage.py upload_tdv_port
50 15 * * 1,2,3,4,5,6 python manage.py upload_tdv_port
```

## Export Capabilities

### Excel Exports
- **Journal Format**: Professional accounting layout
- **Portfolio Reports**: Position and valuation summaries
- **Calculation Details**: Detailed accrual calculations
- **Rate Reports**: Exchange rate histories

### DBF Export
- **Legacy Compatibility**: Support for older accounting systems
- **Structured Format**: Fixed-field database format
- **Automated Mapping**: Chart of accounts translation

### CSV Exports
- **Broker Format**: Raw broker data preservation
- **Analysis Ready**: Pandas-compatible format
- **Historical Data**: Transaction histories

## Security Features

### Authentication
- **Django Admin**: Built-in user management
- **Staff Requirements**: Staff-only access to sensitive data
- **Login Requirements**: Protected views with authentication

### Data Protection
- **Environment Variables**: Sensitive credentials in .env
- **Database Security**: PostgreSQL with proper permissions
- **Audit Trail**: Change history tracking

### Access Control
- **Lock Mechanism**: Period-end data protection
- **Admin Permissions**: Granular permission system
- **History Tracking**: Audit trail

## API Documentation

### Broker APIs

#### IBKR Flex Query API
```python
# Configuration
BASE_URL = "https://ndcdyn.interactivebrokers.com/AccountManagement/FlexWebService"
TOKEN = settings.IBKR_TOKEN
QUERY_ID = settings.IBKR_QUERY

# Usage
response = requests.get(f"{BASE_URL}/SendRequest?t={TOKEN}&q={QUERY_ID}&v=3")
```

#### Tradeville WebSocket API
```python
# Connection
ws_link = "wss://portal.tradeville.ro/"
protocol = "pf4"

# Authentication
login_msg = {
    'cmd': 'login',
    'prm': {
        'coduser': settings.USER_TDV,
        'parola': settings.PASS_TDV,
        'demo': False
    }
}
```

#### BNR XML API
```python
# Current rates
url = "https://bnr.ro/nbrfxrates10days.xml"

# Historical rates
url = "https://www.bnr.ro/files/xml/years/nbrfxrates{year}.xml"
```

## Troubleshooting

### Common Issues

#### Database Connection
```bash
# Check PostgreSQL status
sudo systemctl status postgresql

# Test connection
psql -h localhost -U app -d appdb
```

#### Missing Dependencies
```bash
# Install QuantLib
pip install quantlib-python

# Install system dependencies
sudo apt install libquantlib0-dev
```

#### Broker API Issues
```bash
# Check IBKR token validity
curl "https://ndcdyn.interactivebrokers.com/AccountManagement/FlexWebService/SendRequest?t=TOKEN&q=QUERY&v=3"

# Test TDV connection
python -c "from brokers.management.commands.lib.api_tdv import MyWebSocket; ws = MyWebSocket()"
```

#### File Permissions
```bash
# Set proper permissions
sudo chown -R app:app /home/<USER>/nch/files/
chmod -R 755 /home/<USER>/nch/files/
```

### Logging

#### Command Output
- **Location**: `/home/<USER>/nch/logs/`
- **Files**: `curs_bnr.log`, `api_ibkr.log`, `api_tdv.log`
- **Rotation**: Manual cleanup required

#### Django Logging
```python
# Configuration in settings.py
LOGGING = {
    "version": 1,
    "handlers": {
        "file": {
            "level": "DEBUG",
            "class": "logging.FileHandler",
            "filename": os.path.join(BASE_DIR, '.logs/django.log'),
        },
    },
}
```

### Performance Optimization

#### Database Optimization
```sql
-- Add indexes for frequent queries
CREATE INDEX idx_journal_date ON port_journal(date);
CREATE INDEX idx_journal_custodian ON port_journal(custodian_id);
CREATE INDEX idx_portfolio_date ON port_portfolio(date);
```

#### Query Optimization
```python
# Use select_related for foreign keys
queryset = Journal.objects.select_related(
    'ubo', 'custodian', 'instrument', 'operation'
)

# Use prefetch_related for reverse lookups
queryset = Custodian.objects.prefetch_related('journal_set')
```

## Current Workflow

### Daily Operations
1. **Automated Data Import**: BNR rates, IBKR data, TDV data (via cron)
2. **Portfolio Updates**: Automatic position and valuation updates
3. **Validation**: Automated data validation and error reporting

### Monthly Operations
1. **Journal Preparation**: Generate broker transaction journals
2. **Manual Review**: Validate and adjust entries as needed
3. **Accrual Calculations**: Bond and deposit interest accruals
4. **FX Calculations**: Exchange rate difference calculations
5. **Period Lock**: Lock completed periods from changes

### Quarterly/Annual Operations
1. **Regulatory Reporting**: SAF-T export for tax authorities
2. **Audit Preparation**: Transaction trail export
3. **Performance Analysis**: Detailed P&L attribution

## Future Enhancements

### Planned Features
1. **Libra Bank Integration**: API integration for Romanian bank
2. **Automated Validation**: Enhanced validation against broker calculations
3. **Real-time Monitoring**: Dashboard for operational status
4. **Enhanced Reporting**: Management reporting templates

### Technical Improvements
1. **API Reliability**: Enhanced error handling and retry logic
2. **Performance**: Database optimization and caching
3. **Security**: Enhanced authentication and authorization
4. **Testing**: Comprehensive test suite implementation

---

**Maintainer**: Stelian Mitu 
**Last Updated**: June 2025  
**Version**: 1.0  
**License**: Proprietary