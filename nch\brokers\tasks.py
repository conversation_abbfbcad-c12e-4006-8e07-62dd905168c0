import logging
from nch.nch.celery import shared_task
from django.core.management import call_command

logger = logging.getLogger(__name__)

@shared_task
def fetch_ibkr_data():
    try:
        call_command('api_ibkr')
        logger.info('IBKR data fetched successfully')
    except Exception as e:
        logger.error(f'Error fetching IBKR data: {str(e)}')
        raise

@shared_task
def update_ibkr_portfolio():
    try:
        call_command('upload_ibkr_port')
        logger.info('IBKR portfolio updated successfully')
    except Exception as e:
        logger.error(f'Error updating IBKR portfolio: {str(e)}')
        raise

@shared_task
def fetch_tdv_data():
    try:
        call_command('api_tdv')
        logger.info('Tradeville data fetched successfully')
    except Exception as e:
        logger.error(f'Error fetching Tradeville data: {str(e)}')
        raise

@shared_task
def update_tdv_portfolio():
    try:
        call_command('upload_tdv_port')
        logger.info('Tradeville portfolio updated successfully')
    except Exception as e:
        logger.error(f'Error updating Tradeville portfolio: {str(e)}')
        raise