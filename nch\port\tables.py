import django_tables2 as tables
from .models import Portfolio


class NumberColumn(tables.Column):
    def render(self, value):
        return '{:0.2f}'.format(value)
    
class NumberTable(tables.Table):
    cost = NumberColumn()
    value = NumberColumn()

class PortTable(tables.Table):
    class Meta:
        model = Portfolio
        template_name = "port/port.html"
        fields = ("quantity", )
