# Migration from PostgreSQL to SQLite

This document outlines the changes made to convert the NCH Investment Management System from PostgreSQL to SQLite.

## Changes Made

### 1. Database Configuration (`nch/settings.py`)

**Before (PostgreSQL):**
```python
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': 'appdb',
        'USER': 'app',
        'PASSWORD': os.environ.get('DB_PASS'),
        'HOST': os.environ.get('DB_HOST'),
        'PORT': '5432',
    }
}
```

**After (SQLite):**
```python
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
    }
}
```

### 2. Dependencies (`requirements.txt`)

**Removed:**
- `psycopg2-binary==2.9.9` (PostgreSQL adapter)

**Note:** SQLite support is built into Python, so no additional database drivers are needed.

### 3. Environment Variables

**No longer needed:**
- `DB_PASS` - Database password
- `DB_HOST` - Database host

**Updated `.env` file:**
```env
SECRET_KEY=your_secret_key
IBKR_TOKEN=your_ibkr_token
IBKR_QUERY=your_ibkr_query_id
USER_TDV=your_tradeville_username
PASS_TDV=your_tradeville_password
TDV_DD=tradeville_person_1
TDV_AS=tradeville_person_2
TDV_CP=tradeville_person_3
```

### 4. Installation Process

**Before (PostgreSQL):**
```bash
# Install PostgreSQL
sudo apt install postgresql-16 postgresql-contrib
createdb nch_db
```

**After (SQLite):**
```bash
# No database server installation needed
# SQLite database file is created automatically during migration
python manage.py migrate
```

## Migration Steps

### For New Installations

1. Clone the repository
2. Create virtual environment
3. Install dependencies: `pip install -r requirements.txt`
4. Create `.env` file (without database credentials)
5. Run migrations: `python manage.py migrate`
6. Create superuser: `python manage.py createsuperuser`

### For Existing PostgreSQL Installations

1. **Backup your PostgreSQL data:**
   ```bash
   python manage.py dumpdata > backup.json
   ```

2. **Update the codebase:**
   - Pull the latest changes with SQLite configuration
   - Update your `.env` file (remove DB_PASS and DB_HOST)

3. **Install new dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

4. **Create new SQLite database:**
   ```bash
   python manage.py migrate
   ```

5. **Import your data:**
   ```bash
   python manage.py loaddata backup.json
   ```

6. **Create superuser (if needed):**
   ```bash
   python manage.py createsuperuser
   ```

## SQLite Considerations

### Advantages
- **No server setup required** - SQLite is file-based
- **Cross-platform compatibility** - Works on Windows, Linux, macOS
- **Simplified deployment** - Single database file
- **Built-in Python support** - No additional drivers needed
- **ACID compliance** - Reliable transactions
- **Good performance** - Suitable for small to medium applications

### Limitations
- **Concurrent writes** - Limited compared to PostgreSQL
- **Database size** - Recommended for databases under 1TB
- **Network access** - No built-in network server
- **User management** - No built-in user/role system

### Performance Tips
- **Use transactions** for bulk operations
- **Add indexes** for frequently queried columns
- **Use PRAGMA statements** for optimization:
  ```sql
  PRAGMA journal_mode=WAL;
  PRAGMA synchronous=NORMAL;
  PRAGMA cache_size=10000;
  ```

## Database File Location

The SQLite database file (`db.sqlite3`) will be created in the project root directory. 

**Important:** 
- Include `db.sqlite3` in your `.gitignore` for development
- For production, ensure proper backup procedures for the database file
- Set appropriate file permissions for security

## Backup and Maintenance

### Backup
```bash
# Simple file copy
cp db.sqlite3 backup_$(date +%Y%m%d_%H%M%S).sqlite3

# Using Django
python manage.py dumpdata > backup_$(date +%Y%m%d_%H%M%S).json
```

### Maintenance
```bash
# Vacuum database (reclaim space)
echo "VACUUM;" | sqlite3 db.sqlite3

# Check integrity
echo "PRAGMA integrity_check;" | sqlite3 db.sqlite3
```

## Testing

After migration, test the following functionality:
1. User authentication and admin access
2. Data import commands (IBKR, TDV, BNR)
3. Portfolio and journal views
4. Export functionality
5. Management commands

## Rollback Plan

If you need to rollback to PostgreSQL:
1. Restore the original `settings.py` configuration
2. Add `psycopg2-binary` back to `requirements.txt`
3. Restore PostgreSQL database from backup
4. Update `.env` file with database credentials

## Support

The application code remains unchanged as it uses Django ORM, which provides database abstraction. All existing functionality should work identically with SQLite.
