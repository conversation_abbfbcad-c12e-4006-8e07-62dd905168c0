{% extends "base.html" %}

{% block title %}Jurnal{% endblock %}

{% load render_table from django_tables2 %}

{% block content %}
    <div class="content"> 
        <table class="sortable table table-responsive">

            <thead>
                <tr>
                  <th scope="col" class="col-auto text-start" >Cod</th>
                  <th scope="col" class="col-auto text-start" >Operatie</th>
                  <th scope="col" class="col-auto text-start" >Debit</th>
                  <th scope="col" class="col-auto text-start" >Debit descriere</th>
                  <th scope="col" class="col-auto text-start">Credit</th>
                  <th scope="col" class="col-auto text-start">Credit descriere</th>
                </tr>
              </thead>


            <tbody>
                {% for x in op %}
                <tr>
                    <td>{{ x.operation_code }}</td>
                    <td>{{ x.operation_name }}</td>
                    <td>{{ x.debit__account_code }}</td>
                    <td>{{ x.debit__account_name }}</td>
                    <td>{{ x.credit__account_code  }}</td>
                    <td>{{ x.credit__account_name  }}</td>
                </tr>
                
                {% endfor %}

            </tbody>
        </table>

    </div>
{% endblock %}