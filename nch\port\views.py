from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from django.contrib.admin.views.decorators import staff_member_required
from django.db.models.functions import Round
from django.db.models import OuterRef, Subquery, F
from django_tables2 import SingleTableView
from django.core.management import call_command
from django.contrib import messages
from django.http import JsonResponse, HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.conf import settings
import json
import os
import pandas as pd
from pathlib import Path


from .models import Portfolio, Journal, Bnr, Partner_type, Partner, Custodian, Operation, Accounting
from .tables import PortTable




# Create your views here.

def index(request):
    return render(request, 'port/index.html')


@login_required
def portfolio(request):
    custodians = Portfolio.objects.values('instrument__custodian')\
        .distinct().values_list('instrument__custodian', flat=True)
    
    res = Portfolio.objects.none()
 
    for c in custodians:
        maxdate = Portfolio.objects.filter(instrument__custodian=c)\
            .latest('date').date
        res = res | Portfolio.objects.filter(instrument__custodian=c, date=maxdate)

    return render(request, 'port/portfolio.html', {'portfolio': res})


@login_required
def port(request):
    custodians = Portfolio.objects.values('instrument__custodian')\
        .distinct().values_list('instrument__custodian', flat=True)
    
    res = Portfolio.objects.none()
 
    for c in custodians:
        maxdate = Portfolio.objects.filter(instrument__custodian=c)\
            .latest('date').date
        res = res | Portfolio.objects.filter(instrument__custodian=c, date=maxdate)

    return render(request, 'port/port.html', {'portfolio': res})


@login_required
def jurnal(request):

    # Map Accounting table
    mx = pd.DataFrame(list(Accounting.objects.all().values()))

    def analitic(account, currency, custodian, partner, symbol, credit=True):
        res = str(account)
        has_currency = mx[mx['account_code']==account]['has_currency'].to_list()[0]
        has_custodian_credit = mx[mx['account_code']==account]['has_custodian_credit'].to_list()[0]
        has_custodian_debit = mx[mx['account_code']==account]['has_custodian_debit'].to_list()[0]
        has_partner_credit = mx[mx['account_code']==account]['has_partner_credit'].to_list()[0]
        has_partner_debit = mx[mx['account_code']==account]['has_partner_debit'].to_list()[0]
        has_dot = mx[mx['account_code']==account]['has_dot'].to_list()[0]

        dot = '.' if has_dot else ''
        custodian_detail = mx[mx['account_code']==account]['has_partner_debit'].to_list()[0]

        if has_currency:
            res += dot + currency

        if credit:
            if has_custodian_credit:
                res += dot + custodian
            if has_partner_credit:
                res += dot + partner
        else:
            if has_custodian_debit:
                res += dot + custodian
            if has_partner_debit:
                res += dot + partner            
        
        has_symbol = mx[mx['account_code']==account]['has_symbol'].to_list()[0]
        if has_symbol:
            res += dot + symbol

        # Exceptions
        if (str(account)=='461') and credit and(partner=='BBG'):
            res = '461.BBG'
            
        return res


    jurnal = Journal.objects.values(
            
            'custodian__custodian_type__partner_type_code',
            'custodian__custodian_type__journal_code',

            'ubo__ubo_code', 
            'custodian__custodian_code',
            
            'account__account_code',
            'account__custodian_detail',
            'operation__operation_code', 
            'partner__partner_code', 
            'custodian__custodian_type', 
            'instrument__symbol', 
            'instrument__currency__currency_code', 

            'date',
            'value',
            'bnr',
            'value_ron',
            # 'unit_cost',
            # 'profit',
            
            'quantity',
            'details',
            'transactionid', 

            'operation__credit__account_code',
            'operation__credit__account_name',
            'operation__debit__account_code',
            'operation__debit__account_name',
            'storno',

        ).order_by('date', 'custodian', 'transactionid')
    
    jurnal_res = []
    for row in jurnal:
        currency = row['instrument__currency__currency_code']
        custodian = row['custodian__custodian_code'] + row['account__custodian_detail']
        partner = row['partner__partner_code']
        symbol = row['instrument__symbol']

        row['debit_analitic']= analitic(row['operation__debit__account_code'], currency, custodian, partner, symbol, credit=False)
        row['credit_analitic']= analitic(row['operation__credit__account_code'], currency, custodian, partner, symbol, credit=True)

        if row['storno']:
            row['value_abs'] = - abs(row['value']) 
            row['value_ron_abs'] = - abs(row['value_ron'])
        else:
            row['value_abs'] = abs(row['value'])
            row['value_ron_abs'] = abs(row['value_ron'])


        jurnal_res += row

    return render(request, 'port/jurnal.html', {'jurnal': jurnal})


@login_required
def map_operations(request):
    op = Operation.objects.\
        values(
            'operation_code',
            'operation_name',
            'debit__account_code',
            'debit__account_name',
            'credit__account_code', 
            'credit__account_name',
        ).order_by('operation_code',)
    
    return render(request, 'port/map_operations.html', {'op': op})


@login_required
def helper_mapping(request):
    """View to display and edit helper_mapping.xlsx file"""
    excel_file_path = Path(settings.BASE_DIR) / 'port' / 'helper_files' / 'helper_mapping.xlsx'

    if request.method == 'POST':
        return handle_helper_mapping_save(request, excel_file_path)

    try:
        # Read only the helper_mapping sheet from the Excel file
        try:
            df = pd.read_excel(excel_file_path, sheet_name='helper_mapping', engine='openpyxl')
        except ValueError:
            # If helper_mapping sheet doesn't exist, try to read the first sheet
            excel_data = pd.read_excel(excel_file_path, sheet_name=None, engine='openpyxl')
            if excel_data:
                df = list(excel_data.values())[0]
            else:
                raise ValueError("No data found in Excel file")

        # Replace NaN values with empty strings for better display
        df = df.fillna('')

        # Convert to format suitable for the template
        helper_mapping_data = {
            'columns': df.columns.tolist(),
            'data': df.to_dict('records'),
            'shape': df.shape,
        }

        context = {
            'helper_mapping_data': helper_mapping_data,
            'helper_mapping_json': json.dumps(helper_mapping_data),
            'file_path': str(excel_file_path),
            'file_exists': excel_file_path.exists()
        }

        return render(request, 'port/helper_mapping.html', context)

    except Exception as e:
        context = {
            'error': f"Error reading Excel file: {str(e)}",
            'file_path': str(excel_file_path),
            'file_exists': excel_file_path.exists()
        }
        return render(request, 'port/helper_mapping.html', context)


def handle_helper_mapping_save(request, excel_file_path):
    """Handle saving changes to the helper mapping Excel file"""
    try:
        # Get the JSON data from the request
        data = json.loads(request.body)
        sheet_data = data.get('sheet_data')

        if not sheet_data:
            return JsonResponse({'success': False, 'error': 'Missing data'})

        # Convert the received data back to DataFrame
        df = pd.DataFrame(sheet_data)

        # Create backup of original file
        backup_path = excel_file_path.with_suffix('.backup.xlsx')
        if excel_file_path.exists():
            import shutil
            shutil.copy2(excel_file_path, backup_path)

        # Read existing Excel file to preserve other sheets
        try:
            excel_data = pd.read_excel(excel_file_path, sheet_name=None, engine='openpyxl')
        except FileNotFoundError:
            excel_data = {}

        # Update the helper_mapping sheet
        excel_data['helper_mapping'] = df

        # Write all sheets back to Excel file
        with pd.ExcelWriter(excel_file_path, engine='openpyxl') as writer:
            for name, data_df in excel_data.items():
                data_df.to_excel(writer, sheet_name=name, index=False)

        return JsonResponse({
            'success': True,
            'message': 'Helper mapping saved successfully',
            'backup_created': backup_path.exists()
        })

    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


@csrf_exempt
@login_required
def helper_mapping_api(request):
    """API endpoint for AJAX operations on helper mapping"""
    excel_file_path = Path(settings.BASE_DIR) / 'port' / 'helper_files' / 'helper_mapping.xlsx'

    if request.method == 'POST':
        return handle_helper_mapping_save(request, excel_file_path)

    elif request.method == 'GET':
        action = request.GET.get('action')

        if action == 'get_data':
            try:
                df = pd.read_excel(excel_file_path, sheet_name='helper_mapping', engine='openpyxl')
                df = df.fillna('')

                return JsonResponse({
                    'success': True,
                    'columns': df.columns.tolist(),
                    'data': df.to_dict('records')
                })
            except Exception as e:
                return JsonResponse({'success': False, 'error': str(e)})

    return JsonResponse({'success': False, 'error': 'Invalid request method'})