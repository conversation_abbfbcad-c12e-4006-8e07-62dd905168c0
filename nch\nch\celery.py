import os
from nch.nch.celery import Celery
from celery.schedules import crontab

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'nch.settings')

app = Celery('nch')
app.config_from_object('django.conf:settings', namespace='CELERY')
app.autodiscover_tasks()

# Define your periodic tasks
app.conf.beat_schedule = {
    # BNR Exchange rates tasks
    'bnr-rates-morning': {
        'task': 'port.tasks.fetch_bnr_rates',
        'schedule': crontab(hour=13, minute=30, day_of_week='1-5'),
    },
    'bnr-rates-afternoon': {
        'task': 'port.tasks.fetch_bnr_rates',
        'schedule': crontab(hour=15, minute=30, day_of_week='1-5'),
    },
    
    # IBKR tasks
    'ibkr-morning-fetch': {
        'task': 'brokers.tasks.fetch_ibkr_data',
        'schedule': crontab(hour=8, minute=15, day_of_week='1-6'),
    },
    'ibkr-morning-retry': {
        'task': 'brokers.tasks.fetch_ibkr_data',
        'schedule': crontab(hour=8, minute=45, day_of_week='1-6'),
    },
    'ibkr-portfolio-update': {
        'task': 'brokers.tasks.update_ibkr_portfolio',
        'schedule': crontab(hour=9, minute=15, day_of_week='1-6'),
    },
    
    # Tradeville tasks
    'tdv-morning-fetch': {
        'task': 'brokers.tasks.fetch_tdv_data',
        'schedule': crontab(hour=7, minute=20, day_of_week='1-6'),
    },
    'tdv-afternoon-fetch': {
        'task': 'brokers.tasks.fetch_tdv_data',
        'schedule': crontab(hour=15, minute=30, day_of_week='1-6'),
    },
    'tdv-morning-portfolio': {
        'task': 'brokers.tasks.update_tdv_portfolio',
        'schedule': crontab(hour=7, minute=40, day_of_week='1-6'),
    },
    'tdv-afternoon-portfolio': {
        'task': 'brokers.tasks.update_tdv_portfolio',
        'schedule': crontab(hour=15, minute=50, day_of_week='1-6'),
    },
}