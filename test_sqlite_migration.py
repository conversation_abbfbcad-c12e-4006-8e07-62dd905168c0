#!/usr/bin/env python
"""
Test script to verify SQLite migration is working correctly.
Run this script after migrating to SQLite to ensure everything works.

Usage:
    python test_sqlite_migration.py
"""

import os
import sys
import django
from pathlib import Path

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'nch.settings')
django.setup()

from django.db import connection
from django.core.management import call_command
from port.models import C<PERSON><PERSON>cy, Ubo, Partner_type, <PERSON>ustod<PERSON>, Account, Instrument, Operation, Journal, Portfolio, Bnr

def test_database_connection():
    """Test basic database connectivity"""
    print("Testing database connection...")
    try:
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            if result[0] == 1:
                print("✓ Database connection successful")
                return True
    except Exception as e:
        print(f"✗ Database connection failed: {e}")
        return False

def test_database_file_exists():
    """Check if SQLite database file exists"""
    print("Checking database file...")
    db_path = Path("db.sqlite3")
    if db_path.exists():
        print(f"✓ Database file exists: {db_path.absolute()}")
        print(f"  File size: {db_path.stat().st_size} bytes")
        return True
    else:
        print("✗ Database file does not exist")
        return False

def test_models_creation():
    """Test that models can be created and queried"""
    print("Testing model operations...")
    try:
        # Test Currency model
        currency_count = Currency.objects.count()
        print(f"✓ Currency model accessible, {currency_count} records")
        
        # Test other core models
        models_to_test = [
            (Ubo, "UBO"),
            (Partner_type, "Partner Type"),
            (Custodian, "Custodian"),
            (Account, "Account"),
            (Instrument, "Instrument"),
            (Operation, "Operation"),
            (Journal, "Journal"),
            (Portfolio, "Portfolio"),
            (Bnr, "BNR"),
        ]
        
        for model, name in models_to_test:
            count = model.objects.count()
            print(f"✓ {name} model accessible, {count} records")
        
        return True
    except Exception as e:
        print(f"✗ Model operations failed: {e}")
        return False

def test_foreign_key_relationships():
    """Test foreign key relationships work correctly"""
    print("Testing foreign key relationships...")
    try:
        # Test if we can query with foreign key relationships
        if Currency.objects.exists():
            # Test BNR -> Currency relationship
            bnr_with_currency = Bnr.objects.select_related('currency_code').first()
            if bnr_with_currency:
                print(f"✓ BNR -> Currency relationship works: {bnr_with_currency.currency_code}")
        
        if Journal.objects.exists():
            # Test Journal relationships
            journal_with_relations = Journal.objects.select_related(
                'ubo', 'custodian', 'instrument', 'operation'
            ).first()
            if journal_with_relations:
                print("✓ Journal foreign key relationships work")
        
        print("✓ Foreign key relationships functional")
        return True
    except Exception as e:
        print(f"✗ Foreign key relationship test failed: {e}")
        return False

def test_unique_constraints():
    """Test unique constraints are working"""
    print("Testing unique constraints...")
    try:
        # Test Currency unique constraint
        if Currency.objects.exists():
            first_currency = Currency.objects.first()
            try:
                # Try to create duplicate currency
                Currency.objects.create(
                    currency_code=first_currency.currency_code,
                    currency_name="Test Duplicate"
                )
                print("✗ Unique constraint not working - duplicate created")
                return False
            except Exception:
                print("✓ Currency unique constraint working")
        
        print("✓ Unique constraints functional")
        return True
    except Exception as e:
        print(f"✗ Unique constraint test failed: {e}")
        return False

def test_bulk_operations():
    """Test bulk operations work with SQLite"""
    print("Testing bulk operations...")
    try:
        # Test bulk_create (common operation in the app)
        initial_count = Currency.objects.count()
        
        # Create test currencies
        test_currencies = [
            Currency(currency_code="TST", currency_name="Test Currency"),
            Currency(currency_code="TS2", currency_name="Test Currency 2"),
        ]
        
        Currency.objects.bulk_create(test_currencies, ignore_conflicts=True)
        
        new_count = Currency.objects.count()
        if new_count >= initial_count:
            print("✓ Bulk create operations working")
            
            # Clean up test data
            Currency.objects.filter(currency_code__in=["TST", "TS2"]).delete()
            return True
        else:
            print("✗ Bulk create operations failed")
            return False
    except Exception as e:
        print(f"✗ Bulk operations test failed: {e}")
        return False

def test_admin_interface():
    """Test that admin interface can access models"""
    print("Testing admin interface compatibility...")
    try:
        from django.contrib.admin import site
        
        # Check if models are registered in admin
        registered_models = [model._meta.model for model in site._registry.keys()]
        
        expected_models = [Currency, Ubo, Partner_type, Custodian, Account, 
                          Instrument, Operation, Journal, Portfolio, Bnr]
        
        for model in expected_models:
            if model in registered_models:
                print(f"✓ {model.__name__} registered in admin")
            else:
                print(f"! {model.__name__} not registered in admin (may be normal)")
        
        print("✓ Admin interface compatibility verified")
        return True
    except Exception as e:
        print(f"✗ Admin interface test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("=" * 50)
    print("SQLite Migration Test Suite")
    print("=" * 50)
    
    tests = [
        test_database_file_exists,
        test_database_connection,
        test_models_creation,
        test_foreign_key_relationships,
        test_unique_constraints,
        test_bulk_operations,
        test_admin_interface,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        print()
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} crashed: {e}")
    
    print()
    print("=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! SQLite migration successful.")
        return 0
    else:
        print("⚠️  Some tests failed. Check the output above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
