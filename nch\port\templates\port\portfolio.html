{% extends "base.html" %}

{% block title %}Portfolio{% endblock %}

{% load render_table from django_tables2 %}

{% block content %}
    <div class="content"> 
        <table class="sortable table table-responsive">

            <thead>
                <tr>
                  <th scope="col" class="col-auto text-start" >Entity</th>
                  <th scope="col" class="col-auto text-start">Broker</th>
                  <th scope="col" class="col-md-auto text-start">Symbol</th>
                  <th scope="col" class="col-md-auto text-start">Currency</th>
                  <th scope="col" class="col-md-auto text-end">Quantity</th>
                  <th scope="col" class="col-md-auto text-end">Value</th>
                  <th scope="col" class="col-md-auto text-end">Cost</th>
                </tr>
              </thead>


            <tbody>
                {% for x in portfolio %}

                <tr>
                    <td>{{ x.ubo }}</td>
                    <td>{{ x.instrument.custodian }}</td>
                    <td>{{ x.instrument.symbol }}</td>
                    <td>{{ x.instrument.currency }}</td>
                    <td class="text-end">{{ x.quantity|floatformat:0 }}</td>
                    <td class="text-end">{{ x.value|floatformat:2 }}</td>
                    <td class="text-end">{{ x.cost|floatformat:2 }}</td>
                </tr>
                
                {% endfor %}

            </tbody>
        </table>

    </div>
{% endblock %}