""" API Tradeville """
import websocket 
import time
from IPython.display import display
import pandas as pd
import json
from datetime import datetime
from zoneinfo import ZoneInfo
tzone = ZoneInfo('Europe/Bucharest')

import random
from django.conf import settings 

portal_link = 'https://portal.tradeville.ro'

ws_link = "wss://portal.tradeville.ro/"

DEBUG = False

class MyWebSocket(object):
    def on_message(self, message):
        if DEBUG:
            print(f"Received message: {message}.")

    def on_error(self, error):
        if DEBUG:
            print(f"Received error: {error}.")

    def on_close(self):
        if DEBUG:
            print("On close!")

    def on_open(self, ws):
        if DEBUG:
            print("On open...")  
        
    def str_to_dataframe(self, s):
        # Conversie str > dictionar > dataframe
        result = pd.DataFrame.from_dict(json.loads(s),orient='index').T
        
        cols_explode = []
        for c in result.columns:
            first = str(result[c].to_list()[0])
            if len(first)>0:
                if str(result[c].to_list()[0])[0]=='{':
                    # Serializare coloane - dictionar 
                    result = result.join(pd.DataFrame(result.pop(c).values.tolist()), lsuffix='', rsuffix='_')
        for c in result.columns:
            # Explodare liste pe linii
            item = result[c].tolist()[0]
            if type(item)==list:
                # exclude list of dictionaries
                if type(item[0])!=dict:
                    cols_explode += [c]
                else:
                    dx = {}
                    for d in item:
                        dx.update(d)
                    dx = pd.DataFrame.from_dict(dx,orient='index').T
                    new_col = list(set(dx.columns) - set(result.columns))
                    result = result.join(dx[new_col], how='outer').drop(columns=[c])
        
        if len(cols_explode)>0:
            result = result.explode(cols_explode, ignore_index=True)

        cols = [x for x in list(result.columns) if str(x)[-1]!='_']
        # result = result[cols]
        # Conversie timp epoch > datetime
        if 'time' in cols:
            result['datetime'] = result['time'].apply(lambda x: datetime.fromtimestamp(x/1000, tzone).strftime("%Y-%m-%d, %H:%M:%S"))
        return result


    def __init__(self):
        websocket.enableTrace(False)
        ws = websocket.WebSocket()

        headers = {
            'Accept': "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8",
            'Accept-Encoding': "gzip, deflate",
            "Accept-Language": "en-GB,en;q=0.9",
            'Connection': 'upgrade',
            'Host': 'portal.tradeville.ro',
            'Origin': 'https://portal.tradeville.ro', 
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-WebSocket-Protocol': 'pf4',
            'Upgrade-Insecure-Requests': '1',
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/20100101 Firefox/118.0',
        }
        
        msg = {
            'cmd': 'login',
            'prm': {
                'coduser': settings.USER_TDV,
                'parola': settings.PASS_TDV,
                'demo': False,
            }
        }
        msg = json.dumps(msg)

        self.ws = websocket.create_connection(ws_link, skip_utf8_validation=True,
                origin='https://portal.tradeville.ro',
                subprotocols=["pf4"],
                header=headers)        
        self.ws.on_message = self.on_message
        self.ws.on_error = self.on_error
        self.ws.on_close = self.on_close
        self.ws.on_open = self.on_open
        # self.ws.send('{"cmd":"login","prm":{"coduser":"'+user_tdv+'", "parola":"'+pass_tdv+'","demo": false }}')
        self.ws.send(msg) 
        result = self.ws.recv()
        result = self.str_to_dataframe(result)
        if DEBUG:
            display(result)
            print("Login result", result['OK'].to_list()[0])

    def send_msg(self, message):
        self.ws.send(message)
        result = self.ws.recv()
        try:
            if DEBUG:
                print(result)
            result = self.str_to_dataframe(result)
        except:
            pass
        return result
    
    def recv_msg(self, noOfMessagesToWait):
        for i in range(noOfMessagesToWait):
            time.sleep(.001)
            # De bagat un ping
            result = self.ws.recv()
        try:
            result = self.str_to_dataframe(result)
        except:
            if DEBUG:
                print('Eroare', result)
            else:
                print('Eroare recv_msg')
        return result

    def ordin(self, simbol, pret, cant, csauv, obs='', lagata=100):
        """ Trimitere ordin """
        timeoutID = random.randint(1000,9999)
        nr = random.randint(1000,9999)
        tinta  = str(random.randint(1000000000,9999999999))
        expected = {
            'cmd': 'ordinnoufarapwd',
            "tinta":'x'+tinta,
            'timeoutID': timeoutID,
            "lagata":timeoutID,
        }

        msg = expected.copy()
        msg["prm"] = {
            "simbol":simbol,
            "csauv":csauv,
            "op":"confirma ordin",
            "lapiata":0,
            "parola":"","platf":"p4pt","tipord":"L",
            "cant":cant,
            "pret":pret,
            "valabil":"open",
            "pstop":"",
            "obs":obs},

        result = self.send_msg(json.dumps(msg)) 
        if 'err' in result.columns:
            print('EROARE ORDIN', ' '.join(result['err'].tolist()))

        return result

    def ping(self):
        """ Trimitere ping """
        expected = {
            'timeoutID': random.randint(1000,9999),
            'lagata': random.randint(100,999),
        }
        msg = expected.copy()
        msg['cmd'] = 'ping'
        msg = json.dumps(msg)
        self.ws.send(msg) 
        result = json.loads(self.ws.recv())

         # Check if return is as expected
        for k in expected.keys():
            if expected[k]!=result[k]:
                raise Exception("Unexpected PING return")      
              
        return "pong "+str(result['pong'])
    
    def level2(self, simbol):
        timeoutID = random.randint(1000,9999)
        nr = random.randint(1000,9999)
        idcerere  = random.randint(1000000000000,9999999999999)

        expected = {
            'cmd': 'level2',
            'pagina': 'level2',
            # 'nr': nr,
            # 'simbol': simbol,
            'timeoutID': timeoutID,
            'idcerere': idcerere,
            'tinta':'containerInformatii',
        }

        message = expected.copy()
        message["prm"] = {"simbol": simbol, "nr":nr,}
        message = json.dumps(message)

        #  Interogare        
        self.ws.send(message)
        result = self.ws.recv()
        result = json.loads(result)

        # Check if return is as expected
        for k in expected.keys():
            if expected[k]!=result[k]:
                raise Exception("Unexpected level 2 return") 

        result = pd.DataFrame(result['data']) 

        rename = {'nrobid': 'nr', 'cbid': 'cant', 'bid': 'pret', }
        bid = result[~result['cbid'].isna()][rename.keys()].rename(columns=rename).copy()
        bid['sens'] = 'B'

        rename = {'nroask': 'nr', 'cask': 'cant', 'ask': 'pret', }
        ask = result[~result['cask'].isna()][rename.keys()].rename(columns=rename).copy()
        ask['sens'] = 'S'

        result = pd.concat([bid, ask], ignore_index=True, verify_integrity=True)
        result[['nr', 'cant', ]] = result[['nr', 'cant', ]].astype(int)

        return result.sort_values(by='pret', ignore_index=True)

    def portof(self):
        timeoutID = random.randint(1000,9999)
        lagata = random.randint(1000,9999)
 
        expected = {
            'cmd': 'portof',
            'timeoutID': timeoutID,
            'lagata': lagata,
        }

        message = expected.copy()
        message = json.dumps(message)

        #  Interogare        
        self.ws.send(message)
        result = self.ws.recv()
        result = json.loads(result)

        # Check if return is as expected
        for k in expected.keys():
            if expected[k]!=result[k]:
                raise Exception("Unexpected level 2 return") 

        result = pd.DataFrame(result['data']) 
        result['sold'] = result['sold'].astype(int)
        return result


    def ordine(self):
        timeoutID = random.randint(1000,9999)
        lagata = random.randint(1000,9999)

        # {"cmd":"ordine","tinta":"ordinePortal","prm":{"dstart":null,"simbol":null,"stare":"%"},"timeoutID":49}

        expected = {
            'cmd': 'ordine',
            'tinta': 'ordinePortal',
            'timeoutID': timeoutID,
            'lagata': lagata,
        }

        message = expected.copy()
        message["prm"] = {"dstart":'',"simbol":'',"stare":"%"}
        message = json.dumps(message)

        #  Interogare        
        self.ws.send(message)
        result = self.ws.recv()
        result = json.loads(result)

        # Check if return is as expected
        for k in expected.keys():
            if expected[k]!=result[k]:
                raise Exception("Unexpected level 2 return") 

        if 'err' in result:
            print(result['err'])
        else:
            result = pd.DataFrame(result['data']) 
            result = result[~result['stare'].isin(['A', 'T'])]
            result[['cantr', 'cant', 'cantexec']] = result[['cantr', 'cant', 'cantexec']].fillna(0).astype(int)
            result = result[(result['stare']!='E') | (result['data']>'2023-09-01')].reset_index(drop=True)

            # result.drop(columns=['profit', 'cont', 'market', 'pretev', 'pretn', 'valuta', 'TrdStatus', ], inplace=True)

        return result


    def activitate(self):
        timeoutID = random.randint(1000,9999)
        lagata = random.randint(1000,9999)

        # {"cmd":"ordine","tinta":"ordinePortal","prm":{"dstart":null,"simbol":null,"stare":"%"},"timeoutID":49}

        expected = {
            'cmd': 'activit',
        }

        message = expected.copy()
        message["prm"] = {"dstart":'',"simbol":'',"dend":""}
        message = json.dumps(message)

        #  Interogare        
        self.ws.send(message)
        result = self.ws.recv()
        result = json.loads(result)

        # Check if return is as expected
        for k in expected.keys():
            if expected[k]!=result[k]:
                raise Exception("Unexpected level 2 return") 

        if 'err' in result:
            print(result['err'])
        else:
            result = result['data']
            result = pd.DataFrame(result) 

        return result


    def curs_bnr(self):
        timeoutID = random.randint(1000,9999)
        lagata = random.randint(1000,9999)

        expected = {
            'cmd': 'cursbnr',
        }

        message = expected.copy()
        message["prm"] = {"ccy": "", "dstart":'1oct19', "dend": "31dec99"}
        message = json.dumps(message)

        #  Interogare        
        self.ws.send(message)
        result = self.ws.recv()
        result = json.loads(result)

        # Check if return is as expected
        for k in expected.keys():
            if expected[k]!=result[k]:
                raise Exception("Unexpected level 2 return") 

        if 'err' in result:
            print(result['err'])
        else:
            result = result['data']
            result = pd.DataFrame(result) 

        return result
    

    def close(self):
        print("Close connection")
        self.ws.close()

