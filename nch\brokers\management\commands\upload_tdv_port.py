
""" Export TDV instruments and portfolio"""
from django.core.management.base import BaseCommand, CommandError
from django.conf import settings 
from django.db.models import Max # Generates a "SELECT MAX..." query
from port.models import Instrument, Currency, Ubo, Portfolio, Custodian, Lock

class Command(BaseCommand):

    def handle(self, *args, **options):

        import glob
        import os
        import pandas as pd
        from pathlib import Path
        from datetime import datetime

        broker = 'tdv'
        # root  = str(Path(__file__).resolve().parent.parent.parent) + "/reports/cumulated/tdv_"

        today = str(datetime.today())[:10]
        root = settings.FILE_ROOT + "{broker}/".format(broker=broker)

        lock_date = Lock.objects.aggregate(Max('lock_date')) 
        print('lock_date', lock_date)
        
        # Import relevant tables
        tabs = [
            'activitate', 
            'portof',
            ]

        tab = {}
        
        for t in tabs:
            list_of_files = glob.glob(root + t + '*') 
            latest_file = max(list_of_files, key=os.path.getctime)
            df = pd.read_csv(latest_file).dropna(axis=1, how='all')

            # Remove blank columns 
            if len(df)>1:
                for c in df.columns:
                    uni = list(df[c].unique())
                    if (len(uni)==0): 
                        df.drop(columns=[c], inplace=True) 

            tab[t] = df

        # Import instruments
        cols = ['valuta', 'simbol', 'isin', 'nume', 'firma']
        rename = {
            'valuta': 'currency',
            'simbol': 'symbol',
            'nume': 'name',
            'firma': 'ubo',
            }
        secinfo =  tab['portof'][cols].rename(columns=rename)
        secinfo = secinfo.drop_duplicates(keep='first')
        print(secinfo)


        # Missing instruments
        vals = secinfo['symbol'].unique()
        all_vals = Instrument.objects.values_list('symbol', flat=True)
        missing = list(set(vals) - set(all_vals))
        secinfo = secinfo[secinfo['symbol'].isin(missing)].copy()
        print('Missing instruments:', missing)


        # Upload missing instruments to database
        if len(secinfo)>0:
            model_instances = [
                Instrument(
                    currency = Currency.objects.get(currency_code = row['currency']),
                    custodian = Custodian.objects.get(custodian_code = 'TDV'),
                    symbol = row['symbol'],
                    isin=row['isin'],
                    name=row['name'],
                    type='PLEASE UPDATE',
                    sector='PLEASE UPDATE',
                    country='PLEASE UPDATE',
                )
                for i, row in secinfo.iterrows()]
            # Upload model_instances to database
            unique = ['custodian', 'symbol',]
            update_fields = unique + ['currency', 'isin', 'name', 'type', 'sector', 'country', ]
            # Instrument.objects.bulk_create(
            #     model_instances, 
            #     update_conflicts=True,
            #     unique_fields=unique,
            #     update_fields=update_fields,
            # )



        # Import portfolio
        cols = ['valuta', 'simbol', 'isin', 'nume', 'firma',
                'sold', 'costm', 'ppiata', 
                ]
        rename = {
            'valuta': 'currency',
            'simbol': 'symbol',
            'nume': 'name',
            'firma': 'ubo',
            'sold': 'quantity',
            'costm': 'cost',
            'ppiata': 'value',
            }
        portfolio =  tab['portof'][cols].rename(columns=rename)
        portfolio['cost'] = portfolio['cost'] * portfolio['quantity']
        portfolio['value'] = portfolio['value'] * portfolio['quantity']

        CURRENCIES = ['EUR', 'USD', 'RON', 'GBP', 'CAD', 'SEK',]
        portfolio.loc[portfolio['symbol'].isin(CURRENCIES), 'cost'] = 1

        print(portfolio)


        # Upload portfolio to database
        model_instances = [
            Portfolio(
                ubo = Ubo.objects.get(ubo_code = row['ubo']),
                instrument = Instrument.objects.get(symbol=row['symbol'], custodian__custodian_code='TDV'),
                date=today,
                cost=round(row['cost'], 2),
                value=round(row['value'], 2),
                quantity=round(row['quantity'], 2),
            )
            for i, row in portfolio.iterrows()]
        # Upload model_instances to database
        unique = ['ubo', 'instrument', 'date']
        update_fields = unique + ['cost', 'value', 'quantity',  ]
        # Portfolio.objects.bulk_create(
        #     model_instances, 
        #     update_conflicts=True,
        #     unique_fields=unique,
        #     update_fields=update_fields,
        # )


        print(len(portfolio), 'records for {}'.format(broker))
        print('TDV imported into database', today)


