# Generated by Django 4.2.13 on 2025-08-14 14:35

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import simple_history.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Account",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "custodian_detail",
                    models.CharField(blank=True, default="", max_length=10, null=True),
                ),
                ("account_code", models.CharField(max_length=10, unique=True)),
                ("account_name", models.CharField(default="", max_length=100)),
            ],
        ),
        migrations.CreateModel(
            name="Accounting",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("account_code", models.Char<PERSON>ield(max_length=100, unique=True)),
                ("account_name", models.Char<PERSON>ield(default="", max_length=100)),
                ("has_currency", models.BooleanField(default=False)),
                ("has_custodian_debit", models.BooleanField(default=False)),
                ("has_custodian_credit", models.BooleanField(default=False)),
                ("has_partner_debit", models.BooleanField(default=False)),
                ("has_partner_credit", models.BooleanField(default=False)),
                ("has_symbol", models.BooleanField(default=False)),
                ("has_dot", models.BooleanField(default=False)),
            ],
        ),
        migrations.CreateModel(
            name="Currency",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("currency_code", models.CharField(max_length=3, unique=True)),
                ("currency_name", models.CharField(max_length=100)),
            ],
        ),
        migrations.CreateModel(
            name="Custodian",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("custodian_code", models.CharField(max_length=10, unique=True)),
                ("custodian_name", models.CharField(default="", max_length=100)),
            ],
        ),
        migrations.CreateModel(
            name="Document_type",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "document_type",
                    models.CharField(default="X", max_length=100, unique=True),
                ),
                ("document_name", models.CharField(default="", max_length=100)),
            ],
        ),
        migrations.CreateModel(
            name="Instrument",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("symbol", models.CharField(max_length=100)),
                ("isin", models.CharField(max_length=12)),
                ("name", models.CharField(max_length=100)),
                ("type", models.CharField(max_length=100)),
                ("principal", models.FloatField(blank=True, default=None, null=True)),
                ("face_value", models.FloatField(default=1.0)),
                ("interest", models.FloatField(blank=True, default=None, null=True)),
                ("depo_start", models.DateField(blank=True, default=None, null=True)),
                ("bond_issue", models.DateField(blank=True, default=None, null=True)),
                (
                    "bond_first_coupon",
                    models.DateField(blank=True, default=None, null=True),
                ),
                ("maturity", models.DateField(blank=True, default=None, null=True)),
                (
                    "convention",
                    models.CharField(
                        blank=True, default=None, max_length=50, null=True
                    ),
                ),
                (
                    "calendar",
                    models.CharField(
                        blank=True, default=None, max_length=50, null=True
                    ),
                ),
                (
                    "bond_coupon_count",
                    models.IntegerField(blank=True, default=None, null=True),
                ),
                ("sector", models.CharField(max_length=100)),
                ("country", models.CharField(max_length=100)),
                (
                    "currency",
                    models.ForeignKey(
                        default=1,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="port.currency",
                    ),
                ),
                (
                    "custodian",
                    models.ForeignKey(
                        default=1,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="custodian",
                        to="port.custodian",
                    ),
                ),
            ],
            options={
                "unique_together": {("custodian", "symbol")},
            },
        ),
        migrations.CreateModel(
            name="Lock",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("lock_date", models.DateField(unique=True)),
            ],
        ),
        migrations.CreateModel(
            name="Partner_type",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("partner_type_code", models.CharField(max_length=10, unique=True)),
                ("journal_code", models.CharField(max_length=100)),
            ],
        ),
        migrations.CreateModel(
            name="Ubo",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("ubo_code", models.CharField(max_length=100, unique=True)),
                ("ubo_name", models.CharField(max_length=100)),
                ("ubo_details", models.CharField(default="DD", max_length=100)),
            ],
        ),
        migrations.CreateModel(
            name="Partner",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "partner_code",
                    models.CharField(default="IBKR", max_length=15, unique=True),
                ),
                ("partner_name", models.CharField(default="", max_length=100)),
                (
                    "partner_type",
                    models.ForeignKey(
                        default=1,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="partner_type_id",
                        to="port.partner_type",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Operation",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("operation_code", models.CharField(max_length=100, unique=True)),
                ("operation_name", models.CharField(default="", max_length=100)),
                (
                    "credit",
                    models.ForeignKey(
                        default=1,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="credit",
                        to="port.accounting",
                    ),
                ),
                (
                    "debit",
                    models.ForeignKey(
                        default=1,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="debit",
                        to="port.accounting",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="HistoricalJournal",
            fields=[
                (
                    "id",
                    models.BigIntegerField(
                        auto_created=True, blank=True, db_index=True, verbose_name="ID"
                    ),
                ),
                ("date", models.DateField(default=django.utils.timezone.now)),
                ("transactionid", models.CharField(max_length=100)),
                ("value", models.FloatField()),
                ("value_ron", models.FloatField(default=0)),
                ("bnr", models.FloatField(default=1.0)),
                ("quantity", models.FloatField()),
                ("details", models.CharField(max_length=250)),
                ("storno", models.BooleanField(default=False)),
                ("lock", models.BooleanField(default=False)),
                ("history_id", models.AutoField(primary_key=True, serialize=False)),
                ("history_date", models.DateTimeField(db_index=True)),
                ("history_change_reason", models.CharField(max_length=100, null=True)),
                (
                    "history_type",
                    models.CharField(
                        choices=[("+", "Created"), ("~", "Changed"), ("-", "Deleted")],
                        max_length=1,
                    ),
                ),
                (
                    "account",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="port.account",
                    ),
                ),
                (
                    "custodian",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="port.custodian",
                    ),
                ),
                (
                    "history_user",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "instrument",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="port.instrument",
                    ),
                ),
                (
                    "operation",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="port.operation",
                    ),
                ),
                (
                    "partner",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="port.partner",
                    ),
                ),
                (
                    "ubo",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        default=1,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="port.ubo",
                    ),
                ),
            ],
            options={
                "verbose_name": "historical journal",
                "verbose_name_plural": "historical journals",
                "ordering": ("-history_date", "-history_id"),
                "get_latest_by": ("history_date", "history_id"),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name="Document",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("id_doc", models.CharField(max_length=10, unique=True)),
                ("name", models.CharField(max_length=100)),
                ("date", models.DateField(default=django.utils.timezone.now)),
                (
                    "document_type",
                    models.ForeignKey(
                        default=1,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="port.document_type",
                    ),
                ),
                (
                    "partner_code",
                    models.ForeignKey(
                        default=1,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="port.partner",
                    ),
                ),
            ],
        ),
        migrations.AddField(
            model_name="custodian",
            name="custodian_type",
            field=models.ForeignKey(
                default=1,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="custodian_type_id",
                to="port.partner_type",
            ),
        ),
        migrations.AddField(
            model_name="account",
            name="currency",
            field=models.ForeignKey(
                default=1,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="currency",
                to="port.currency",
            ),
        ),
        migrations.AddField(
            model_name="account",
            name="custodian",
            field=models.ForeignKey(
                default=1,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="account_custodian",
                to="port.custodian",
            ),
        ),
        migrations.AddField(
            model_name="account",
            name="ubo",
            field=models.ForeignKey(
                default=1,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="account_ubo",
                to="port.ubo",
            ),
        ),
        migrations.CreateModel(
            name="Portfolio",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("date", models.DateField()),
                ("cost", models.FloatField()),
                ("value", models.FloatField()),
                ("quantity", models.FloatField()),
                ("accruedint", models.FloatField(default=0)),
                (
                    "instrument",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="port.instrument",
                    ),
                ),
                (
                    "ubo",
                    models.ForeignKey(
                        default=1,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="port.ubo",
                    ),
                ),
            ],
            options={
                "unique_together": {("ubo", "instrument", "date")},
            },
        ),
        migrations.CreateModel(
            name="Journal",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("date", models.DateField(default=django.utils.timezone.now)),
                ("transactionid", models.CharField(max_length=100)),
                ("value", models.FloatField()),
                ("value_ron", models.FloatField(default=0)),
                ("bnr", models.FloatField(default=1.0)),
                ("quantity", models.FloatField()),
                ("details", models.CharField(max_length=250)),
                ("storno", models.BooleanField(default=False)),
                ("lock", models.BooleanField(default=False)),
                (
                    "account",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="port.account"
                    ),
                ),
                (
                    "custodian",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="port.custodian"
                    ),
                ),
                (
                    "instrument",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="port.instrument",
                    ),
                ),
                (
                    "operation",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="port.operation"
                    ),
                ),
                (
                    "partner",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="port.partner"
                    ),
                ),
                (
                    "ubo",
                    models.ForeignKey(
                        default=1,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="port.ubo",
                    ),
                ),
            ],
            options={
                "unique_together": {("ubo", "custodian", "account", "transactionid")},
            },
        ),
        migrations.CreateModel(
            name="Deposits",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("principal", models.DecimalField(decimal_places=2, max_digits=15)),
                ("interest_rate", models.FloatField()),
                ("start", models.DateField()),
                ("maturity", models.DateField()),
                (
                    "convention",
                    models.CharField(
                        choices=[
                            ("360", "360"),
                            ("365", "365"),
                            ("ACT/ACT", "ACT/ACT"),
                        ],
                        max_length=10,
                    ),
                ),
                (
                    "interest_amount",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=15, null=True
                    ),
                ),
                (
                    "interest_calculated",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=15, null=True
                    ),
                ),
                (
                    "check_actual_vs_calc",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=15, null=True
                    ),
                ),
                ("new_deposit", models.BooleanField(blank=True, null=True)),
                ("liquidated", models.BooleanField(blank=True, null=True)),
                ("details", models.CharField(blank=True, max_length=255, null=True)),
                (
                    "deposit",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="port.instrument",
                    ),
                ),
            ],
            options={
                "verbose_name": "Deposit",
                "verbose_name_plural": "Deposits",
                "unique_together": {("deposit", "maturity")},
            },
        ),
        migrations.CreateModel(
            name="Bnr",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("date", models.DateField(verbose_name="date published")),
                ("value", models.FloatField()),
                ("value_exact", models.FloatField(blank=True, default=None, null=True)),
                (
                    "currency_code",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="port.currency"
                    ),
                ),
            ],
            options={
                "unique_together": {("currency_code", "date")},
            },
        ),
        migrations.AlterUniqueTogether(
            name="account",
            unique_together={("ubo", "account_code")},
        ),
    ]
